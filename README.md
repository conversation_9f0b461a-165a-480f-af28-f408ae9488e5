# MonkeyType Automation MCP Server

An MCP (Model Context Protocol) server that automates typing tests on [MonkeyType.com](https://monkeytype.com) using browser automation.

## Features

- **Fast Automation**: Types as fast as technically possible with no artificial delays
- **Browser Automation**: Uses <PERSON>wright to interact with MonkeyType website
- **Real-time Word Detection**: Detects active words using DOM selectors
- **MCP Protocol**: Implements the Model Context Protocol for integration with MCP clients

## Installation

1. Install dependencies:
```bash
npm install
```

2. Install Playwright browsers:
```bash
npx playwright install chromium
```

3. Build the TypeScript code:
```bash
npm run build
```

## Usage

### As an MCP Server

Start the server in stdio mode:
```bash
node dist/index.js
```

The server provides these tools:

- **start_automation**: Opens browser, navigates to MonkeyType, and begins automated typing
- **stop_automation**: Stops the current automation
- **close_browser**: Closes the browser and cleans up resources

### Testing the Server

Run the test script to verify the server works:
```bash
node test-mcp.js
```

### Integration with MCP Clients

Add this server to your MCP client configuration. For example, in Claude <PERSON>:

```json
{
  "mcpServers": {
    "monkeytype-automation": {
      "command": "node",
      "args": ["/path/to/monkey-type/dist/index.js"],
      "cwd": "/path/to/monkey-type"
    }
  }
}
```

## How It Works

1. **Browser Launch**: Opens Chromium browser in non-headless mode
2. **Navigation**: Goes to monkeytype.com and waits for the typing test to load
3. **Word Detection**: Finds the active word using CSS selector `.word.active`
4. **Typing**: Types each character of the word using Playwright's keyboard API
5. **Progression**: Presses space to move to the next word
6. **Completion**: Continues until all words are typed or test completes

## DOM Structure

The automation works with MonkeyType's DOM structure:

- Words container: `#words`
- Active word: `.word.active`
- Individual letters: Children of each word element

## Technical Details

- **Platform**: macOS M1 Air (but should work on other platforms)
- **Browser**: Chromium via Playwright
- **Protocol**: MCP (Model Context Protocol)
- **Language**: TypeScript/Node.js
- **Speed**: No artificial delays - types as fast as possible

## Development

- `src/index.ts`: Main MCP server implementation
- `test-mcp.js`: Test script for verifying server functionality
- `package.json`: Dependencies and scripts
- `tsconfig.json`: TypeScript configuration

## Notes

- The browser opens in non-headless mode so you can see the automation in action
- The automation respects MonkeyType's DOM structure and doesn't use any exploits
- Speed is limited only by browser and network performance
- The server handles cleanup properly when stopped

## License

MIT License
