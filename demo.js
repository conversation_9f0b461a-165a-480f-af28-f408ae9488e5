#!/usr/bin/env node

import { spawn } from 'child_process';
import { createInterface } from 'readline';

console.log('🚀 MonkeyType Automation Demo');
console.log('This will start the automation and begin typing on MonkeyType.com');
console.log('Press Ctrl+C to stop the automation at any time.\n');

// Start the MCP server
const mcpServer = spawn('node', ['dist/index.js'], {
  stdio: ['pipe', 'pipe', 'inherit']
});

const rl = createInterface({
  input: mcpServer.stdout,
  output: process.stdout,
  terminal: false
});

let requestId = 1;

function sendRequest(method, params = {}) {
  const request = {
    jsonrpc: "2.0",
    id: requestId++,
    method: method,
    params: params
  };
  
  mcpServer.stdin.write(JSON.stringify(request) + '\n');
  return requestId - 1;
}

// Initialize the server
console.log('Initializing MCP server...');
const initId = sendRequest("initialize", {
  protocolVersion: "2024-11-05",
  capabilities: {},
  clientInfo: {
    name: "monkeytype-demo",
    version: "1.0.0"
  }
});

// Listen for responses
rl.on('line', (line) => {
  try {
    const response = JSON.parse(line);
    
    if (response.id === initId) {
      console.log('✅ MCP server initialized');
      console.log('🌐 Starting MonkeyType automation...');
      console.log('📝 This will open a browser window and begin typing automatically');
      
      // Start the automation
      sendRequest("tools/call", {
        name: "start_automation",
        arguments: {}
      });
    } else if (response.method === undefined && response.result) {
      // Tool call response
      if (response.result.content && response.result.content[0]) {
        console.log('🎯 Result:', response.result.content[0].text);
      }
    } else if (response.error) {
      console.error('❌ Error:', response.error.message);
    }
  } catch (error) {
    // Ignore non-JSON output
  }
});

mcpServer.on('error', (error) => {
  console.error('❌ MCP Server error:', error);
});

mcpServer.on('close', (code) => {
  console.log(`\n🏁 MCP Server exited with code ${code}`);
  process.exit(code);
});

// Handle cleanup
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping automation...');
  
  // Send stop automation request
  sendRequest("tools/call", {
    name: "stop_automation",
    arguments: {}
  });
  
  // Send close browser request
  setTimeout(() => {
    sendRequest("tools/call", {
      name: "close_browser",
      arguments: {}
    });
    
    setTimeout(() => {
      mcpServer.kill();
      process.exit(0);
    }, 1000);
  }, 1000);
});
