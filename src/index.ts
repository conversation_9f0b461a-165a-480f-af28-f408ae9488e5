#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { chromium, Browser, Page } from 'playwright';

class MonkeyTypeAutomation {
  private browser: Browser | null = null;
  private page: Page | null = null;
  private isRunning = false;

  async initBrowser(): Promise<void> {
    if (!this.browser) {
      this.browser = await chromium.launch({ 
        headless: false,
        slowMo: 0 // No delay between actions
      });
      this.page = await this.browser.newPage();
    }
  }

  async navigateToMonkeyType(): Promise<void> {
    if (!this.page) throw new Error('Browser not initialized');
    
    await this.page.goto('https://monkeytype.com');
    await this.page.waitForLoadState('networkidle');
    
    // Wait for the typing test to be ready
    await this.page.waitForSelector('#words', { timeout: 10000 });
  }

  async getCurrentWord(): Promise<string | null> {
    if (!this.page) return null;

    try {
      // Look for the active word (has class 'word active')
      const activeWord = await this.page.evaluate(() => {
        const wordsWrapper = document.querySelector('#words');
        if (!wordsWrapper) return null;

        // Find the current active word - it has class 'word active'
        const activeWordElement = wordsWrapper.querySelector('.word.active');
        if (activeWordElement) {
          return activeWordElement.textContent?.trim() || null;
        }

        // Fallback: find the first word that doesn't have 'correct' class
        const words = wordsWrapper.querySelectorAll('.word');
        for (const word of words) {
          if (!word.classList.contains('correct') && !word.classList.contains('incorrect')) {
            return word.textContent?.trim() || null;
          }
        }
        return null;
      });

      return activeWord;
    } catch (error) {
      console.error('Error getting current word:', error);
      return null;
    }
  }

  async typeWord(word: string): Promise<void> {
    if (!this.page) throw new Error('Browser not initialized');

    // Type each character of the word
    for (const char of word) {
      await this.page.keyboard.press(char);
    }
    
    // Press space to move to next word
    await this.page.keyboard.press('Space');
  }

  async isTestComplete(): Promise<boolean> {
    if (!this.page) return true;

    try {
      // Check if test is complete by looking for results or if no more words
      const isComplete = await this.page.evaluate(() => {
        // Check if results are showing
        const results = document.querySelector('#result');
        if (results && results.classList.contains('hidden') === false) {
          return true;
        }

        // Check if all words are completed
        const wordsWrapper = document.querySelector('#words');
        if (!wordsWrapper) return true;

        const words = wordsWrapper.querySelectorAll('.word');
        const remainingWords = Array.from(words).filter(word =>
          !word.classList.contains('correct') && !word.classList.contains('incorrect')
        );

        return remainingWords.length === 0;
      });

      return isComplete;
    } catch (error) {
      console.error('Error checking test completion:', error);
      return true;
    }
  }

  async startAutomation(): Promise<string> {
    if (this.isRunning) {
      return 'Automation is already running';
    }

    try {
      this.isRunning = true;
      await this.initBrowser();
      await this.navigateToMonkeyType();

      let wordsTyped = 0;
      let lastWord = '';

      while (!await this.isTestComplete() && this.isRunning) {
        const currentWord = await this.getCurrentWord();
        
        if (!currentWord) {
          // Wait a bit and try again
          await this.page!.waitForTimeout(50);
          continue;
        }

        // Avoid typing the same word repeatedly
        if (currentWord === lastWord) {
          await this.page!.waitForTimeout(50);
          continue;
        }

        await this.typeWord(currentWord);
        wordsTyped++;
        lastWord = currentWord;

        // Small delay to let the page update
        await this.page!.waitForTimeout(10);
      }

      this.isRunning = false;
      return `Automation completed! Typed ${wordsTyped} words.`;
    } catch (error) {
      this.isRunning = false;
      throw new Error(`Automation failed: ${error}`);
    }
  }

  async stopAutomation(): Promise<string> {
    this.isRunning = false;
    return 'Automation stopped';
  }

  async closeBrowser(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.page = null;
    }
  }
}

const automation = new MonkeyTypeAutomation();

const server = new Server(
  {
    name: 'monkeytype-automation',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: 'start_automation',
        description: 'Start MonkeyType automation - opens browser and begins typing test automatically',
        inputSchema: {
          type: 'object',
          properties: {},
        },
      },
      {
        name: 'stop_automation',
        description: 'Stop the current automation',
        inputSchema: {
          type: 'object',
          properties: {},
        },
      },
      {
        name: 'close_browser',
        description: 'Close the browser and clean up resources',
        inputSchema: {
          type: 'object',
          properties: {},
        },
      },
    ],
  };
});

server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name } = request.params;

  try {
    switch (name) {
      case 'start_automation':
        const result = await automation.startAutomation();
        return {
          content: [
            {
              type: 'text',
              text: result,
            },
          ],
        };

      case 'stop_automation':
        const stopResult = await automation.stopAutomation();
        return {
          content: [
            {
              type: 'text',
              text: stopResult,
            },
          ],
        };

      case 'close_browser':
        await automation.closeBrowser();
        return {
          content: [
            {
              type: 'text',
              text: 'Browser closed successfully',
            },
          ],
        };

      default:
        throw new Error(`Unknown tool: ${name}`);
    }
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error: ${error}`,
        },
      ],
      isError: true,
    };
  }
});

async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  
  // Handle cleanup on exit
  process.on('SIGINT', async () => {
    await automation.closeBrowser();
    process.exit(0);
  });
}

main().catch((error) => {
  console.error('Server error:', error);
  process.exit(1);
});
