#!/usr/bin/env node

import { spawn } from 'child_process';
import { createInterface } from 'readline';

// Start the MCP server
const mcpServer = spawn('node', ['dist/index.js'], {
  stdio: ['pipe', 'pipe', 'inherit']
});

const rl = createInterface({
  input: mcpServer.stdout,
  output: process.stdout,
  terminal: false
});

// Send initialization request
const initRequest = {
  jsonrpc: "2.0",
  id: 1,
  method: "initialize",
  params: {
    protocolVersion: "2024-11-05",
    capabilities: {},
    clientInfo: {
      name: "test-client",
      version: "1.0.0"
    }
  }
};

console.log('Sending initialization request...');
mcpServer.stdin.write(JSON.stringify(initRequest) + '\n');

// Listen for responses
rl.on('line', (line) => {
  try {
    const response = JSON.parse(line);
    console.log('Received:', JSON.stringify(response, null, 2));
    
    if (response.id === 1) {
      // Send list tools request
      const listToolsRequest = {
        jsonrpc: "2.0",
        id: 2,
        method: "tools/list",
        params: {}
      };
      
      console.log('\nSending list tools request...');
      mcpServer.stdin.write(JSON.stringify(listToolsRequest) + '\n');
    }
    
    if (response.id === 2) {
      console.log('\nMCP Server is working! Available tools:');
      response.result.tools.forEach(tool => {
        console.log(`- ${tool.name}: ${tool.description}`);
      });
      
      console.log('\nTo start automation, you can call the start_automation tool.');
      console.log('Closing test...');
      mcpServer.kill();
      process.exit(0);
    }
  } catch (error) {
    console.log('Raw output:', line);
  }
});

mcpServer.on('error', (error) => {
  console.error('MCP Server error:', error);
});

mcpServer.on('close', (code) => {
  console.log(`MCP Server exited with code ${code}`);
  process.exit(code);
});

// Handle cleanup
process.on('SIGINT', () => {
  mcpServer.kill();
  process.exit(0);
});
